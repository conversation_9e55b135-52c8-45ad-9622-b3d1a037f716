#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用配置文件

包含系统运行所需的各种配置参数
"""

import os
from typing import List, Optional, Union

# 基础配置
DEBUG = os.getenv("DEBUG", "True").lower() in ["true", "1", "yes"]
APP_NAME = "金融槽位提取服务"

class ESConfig:
    ES_HOSTS = ["http://**********:9200"]
    ES_USER = "elastic"
    ES_PASSWORD = "yourpassword"  # 使用您在docker-compose中设置的密码
    ES_AUTH_REQUIRED = False
    ES_VERIFY_CERTS = False
    ES_TIMEOUT = 30
    ES_MAX_RETRIES = 3
    ES_INDEX_NAME = "slot_extraction"