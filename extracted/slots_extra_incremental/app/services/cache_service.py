#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缓存服务模块

此模块用于在服务启动后，定期从Elasticsearch获取槽位词库和正则表达式，
并维护全局缓存，供槽位抽取器使用。
缓存每五分钟更新一次，减少对ES的直接查询，提高性能。
如果ES服务出现问题，或槽位数据异常，则使用已缓存数据作为兜底，保证槽位抽取器正常运行。

ES中存储的数据都是正则表达式字符串格式：
- keywords类型存储的是由关键词列表生成的正则表达式字符串
- regexs类型存储的是直接编写的正则表达式字符串
- tool_regexs类型存储的是工具类正则表达式字符串（如金额、收益率等）

缓存服务会将从ES获取的正则表达式字符串编译为正则表达式对象后分别存储在三个缓存中：
- _keywords_cache: 存储由关键词列表生成的正则表达式对象
- _regexs_cache: 存储直接编写的正则表达式对象
- _tool_regexs_cache: 存储工具类正则表达式对象
"""

import threading
import re
from typing import Dict, Union, Optional
from datetime import datetime

# 不在顶层直接导入es_service，避免循环导入
from app.utils.extractor.shared.shared_utils import logger

class CacheService:
    """缓存服务类，用于维护槽位词库和正则表达式的全局缓存"""
    
    def __init__(self):
        """初始化缓存服务"""
        self.REGEXS_PREFIX = "regexs"
        self.KEYWORDS_PREFIX = "keywords"
        self.TOOL_REGEXS_PREFIX = "tool_regexs"
        
        # 缓存数据结构
        self._keywords_cache: Dict[str, re.Pattern] = {}  # 槽位词库编译后的正则模式
        self._regexs_cache: Dict[str, re.Pattern] = {}    # 槽位正则缓存
        self._tool_regexs_cache: Dict[str, re.Pattern] = {}  # 工具类正则缓存
        
        # 缓存更新时间
        self._last_update_time: Optional[datetime] = None
        
        # 缓存锁，用于线程安全访问
        self._cache_lock = threading.RLock()
        
        # 更新定时器
        self._update_timer: Optional[threading.Timer] = None
        
        # 更新间隔（秒）
        self._update_interval = 300  # 5分钟
        
        # 服务是否运行
        self._running = False
    
    def start(self) -> bool:
        """
        启动缓存服务
        
        Returns:
            bool: 启动是否成功
        """
        logger.info("正在启动缓存服务...")
        
        if self._running:
            logger.warning("缓存服务已经在运行")
            return True
        
        # 执行首次缓存更新
        success = self._update_cache()
        
        if success:
            self._running = True
            # 启动定时更新
            self._schedule_next_update()
            logger.info("缓存服务启动成功")
        else:
            logger.error("缓存服务启动失败：首次缓存更新失败")
        
        return success
    
    def stop(self):
        """停止缓存服务"""
        logger.info("正在停止缓存服务...")
        
        if not self._running:
            logger.warning("缓存服务未在运行")
            return
        
        # 取消定时更新
        if self._update_timer:
            self._update_timer.cancel()
            self._update_timer = None
        
        self._running = False
        logger.info("缓存服务已停止")
    
    def _schedule_next_update(self):
        """安排下一次缓存更新"""
        if not self._running:
            return
            
        if self._update_timer:
            self._update_timer.cancel()
            
        # 创建新的定时器
        self._update_timer = threading.Timer(self._update_interval, self._timed_update)
        self._update_timer.daemon = True
        self._update_timer.start()
    
    def _timed_update(self):
        """定时更新执行函数"""
        try:
            logger.debug("执行定时缓存更新...")
            self._update_cache()
        except Exception as e:
            logger.error(f"定时缓存更新失败: {str(e)}")
        finally:
            # 安排下一次更新
            self._schedule_next_update()
    
    def _update_cache(self) -> bool:
        """
        从ES更新缓存数据
        
        Returns:
            bool: 更新是否成功
        """
        # 延迟导入es_service，避免循环导入
        from app.services.es_service import es_service
        
        # 检查ES连接
        if not es_service.is_connected():
            logger.warning("ES连接不可用，尝试重新连接...")
            if not es_service.reconnect():
                logger.error("无法连接到ES，缓存更新失败")
                return False
        
        # 获取关键词正则数据 
        keywords_regex_data = es_service.get_all_data_by_type("keywords")
        # 获取正则数据 
        regexs_data = es_service.get_all_data_by_type("regexs")
        # 获取工具正则数据
        tool_regexs_data = es_service.get_all_data_by_type("tool_regexs")
        
        # 如果三者都为空，可能是查询失败
        if not keywords_regex_data and not regexs_data and not tool_regexs_data:
            logger.warning("从ES获取的数据为空，缓存更新可能不完整")
            return False
        
        update_success = True
        # 更新缓存
        with self._cache_lock:
            # 临时存储成功编译的关键词正则
            new_keywords_cache = self._keywords_cache.copy()
            
            # 处理keywords数据 - 基于已有key更新或添加新key
            if keywords_regex_data:
                for doc_id, regex_str in keywords_regex_data.items():
                    try:
                        # 确保regex_str是字符串且非空
                        if regex_str and isinstance(regex_str, str):
                            compiled_pattern = re.compile(regex_str, re.VERBOSE)
                            new_keywords_cache[doc_id] = compiled_pattern
                        else:
                            logger.warning(f"关键词正则表达式 {doc_id} 不是字符串类型或为空，保留原有缓存")
                    except Exception as e:
                        logger.error(f"编译关键词正则表达式 {doc_id} 失败: {str(e)}，保留原有缓存")
                        update_success = False
            
            # 更新词库缓存
            self._keywords_cache = new_keywords_cache
            
            # 处理regexs数据 
            new_regexs_cache = self._regexs_cache.copy()
            
            if regexs_data:
                # 编译正则表达式
                for regex_id, regex_str in regexs_data.items():
                    try:
                        # 确保regex_str是字符串且非空
                        if regex_str and isinstance(regex_str, str):
                            compiled_pattern = re.compile(regex_str, re.VERBOSE)
                            new_regexs_cache[regex_id] = compiled_pattern
                        else:
                            logger.warning(f"正则表达式 {regex_id} 不是字符串类型或为空，保留原有缓存")
                    except Exception as e:
                        logger.error(f"编译正则表达式 {regex_id} 失败: {str(e)}，保留原有缓存")
            
            # 更新正则缓存
            self._regexs_cache = new_regexs_cache
            
            # 处理tool_regexs数据
            new_tool_regexs_cache = self._tool_regexs_cache.copy()
            
            if tool_regexs_data:
                # 编译工具类正则表达式
                for regex_id, regex_str in tool_regexs_data.items():
                    try:
                        # 确保regex_str是字符串且非空
                        if regex_str and isinstance(regex_str, str):
                            compiled_pattern = re.compile(regex_str, re.VERBOSE)
                            new_tool_regexs_cache[regex_id] = compiled_pattern
                        else:
                            logger.warning(f"工具类正则表达式 {regex_id} 不是字符串类型或为空，保留原有缓存")
                    except Exception as e:
                        logger.error(f"编译工具类正则表达式 {regex_id} 失败: {str(e)}，保留原有缓存")
            
            # 更新工具类正则缓存
            self._tool_regexs_cache = new_tool_regexs_cache

            # 更新时间
            self._last_update_time = datetime.now()
            
            logger.info(f"缓存更新完成，理财条件词库匹配模式: {len(self._keywords_cache)}个, 正则匹配模式: {len(self._regexs_cache)}个, 工具类正则数量: {len(self._tool_regexs_cache)}条, 时间: {self._last_update_time}")
        
        return update_success
    
    def get_keywords(self, slot_name: Optional[str] = None, slot_value: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern, None]:
        """
        获取编译后的关键词正则缓存，支持按槽位名称和槽位值过滤
        
        Args:
            slot_name: 槽位名称，如果指定则只返回该槽位的正则
            slot_value: 槽位值，如果指定则只返回特定槽位值的正则
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern, None]: 编译后的关键词正则缓存字典或单个正则表达式
        """
        with self._cache_lock:
            if not slot_name and not slot_value:
                return self._keywords_cache.copy()
            
            filtered_keywords = {}
            prefix = self.KEYWORDS_PREFIX
            
            if slot_name and slot_value:
                # 精确匹配特定slot_name和slot_value
                doc_id = f"{prefix}_{slot_name}_{slot_value}"
                if doc_id in self._keywords_cache:
                    return self._keywords_cache[doc_id]
            elif slot_name:
                # 匹配特定slot_name的所有slot_value
                pattern = f"{prefix}_{slot_name}_"
                for key, value in self._keywords_cache.items():
                    if key.startswith(pattern):
                        slot_value = key.split(pattern)[1]
                        filtered_keywords[slot_value] = value
            elif slot_value:
                # 匹配特定slot_value的所有slot_name
                pattern = f"_{slot_value}"
                for key, value in self._keywords_cache.items():
                    if key.startswith(prefix) and key.endswith(pattern):
                        slot_name = key.split(prefix)[1].split(pattern)[0]
                        filtered_keywords[slot_name] = value
            
            return filtered_keywords
    
    def get_regexs(self, slot_name: Optional[str] = None, slot_value: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern, None]:
        """
        获取正则缓存，支持按槽位名称和槽位值过滤
        
        Args:
            slot_name: 槽位名称，如果指定则只返回该槽位的正则
            slot_value: 槽位值，如果指定则只返回特定槽位值的正则
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern, None]: 正则缓存字典、单个正则表达式或None
        """
        with self._cache_lock:
            if not slot_name and not slot_value:
                return self._regexs_cache.copy()
            
            filtered_regexs = {}
            prefix = self.REGEXS_PREFIX
            
            if slot_name and slot_value:
                # 精确匹配特定slot_name和slot_value
                doc_id = f"{prefix}_{slot_name}_{slot_value}"
                if doc_id in self._regexs_cache:
                    return self._regexs_cache[doc_id]
            elif slot_name:
                # 匹配特定slot_name的所有slot_value
                pattern = f"{prefix}_{slot_name}_"
                for key, value in self._regexs_cache.items():
                    if key.startswith(pattern):
                        slot_value = key.split(pattern)[1]
                        filtered_regexs[slot_value] = value
            elif slot_value:
                # 匹配特定slot_value的所有slot_name
                pattern = f"_{slot_value}"
                for key, value in self._regexs_cache.items():
                    if key.startswith(prefix) and key.endswith(pattern):
                        slot_name = key.split(prefix)[1].split(pattern)[0]
                        filtered_regexs[slot_name] = value
            
            return filtered_regexs
    
    def get_tool_regexs(self, slot_name: Optional[str] = None, tool_name: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern, None]:
        """
        获取工具类正则缓存，支持按工具类型和工具名称过滤
        
        Args:
            slot_name: 槽位类型（如'amount'或'rate'或'risk'），可选
            tool_name: 工具名称，可选
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern, None]: 工具类正则缓存字典或单个正则表达式或None
        """
        with self._cache_lock:
            if not slot_name and not tool_name:
                return self._tool_regexs_cache.copy()
            
            filtered_tool_regexs = {}
            tool_regexs_prefix = self.TOOL_REGEXS_PREFIX
            if slot_name and tool_name:
                # 精确匹配特定tool_type和tool_name
                doc_id = f"{tool_regexs_prefix}_{slot_name}_{tool_name}"
                if doc_id in self._tool_regexs_cache:
                    return self._tool_regexs_cache[doc_id]
            elif slot_name:
                # 匹配特定tool_type的所有tool_name
                doc_prefix = f"{tool_regexs_prefix}_{slot_name}_"
                for key, value in self._tool_regexs_cache.items():
                    if key.startswith(doc_prefix):
                        tool_name = key.split(doc_prefix)[1]
                        filtered_tool_regexs[tool_name] = value
            elif tool_name:
                # 匹配特定tool_name
                for key, value in self._tool_regexs_cache.items():
                    if key.endswith(f"_{tool_name}"):
                        filtered_tool_regexs[tool_name] = value
            
            return filtered_tool_regexs
    
    def force_update_and_get_keywords_by_slot(self, slot_name: str, slot_value: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern]:
        """
        强制更新并获取特定槽位的关键词正则
        
        Args:
            slot_name: 槽位名称
            slot_value: 槽位值，如果不指定则返回该槽位下的所有值
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern]: 编译后的关键词正则
        """
        self.force_update()
        return self.get_keywords(slot_name, slot_value)
    
    def force_update_and_get_regexs_by_slot(self, slot_name: str, slot_value: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern, None]:
        """
        强制更新并获取特定槽位的正则表达式
        
        Args:
            slot_name: 槽位名称
            slot_value: 槽位值，如果不指定则返回该槽位下的所有值
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern, None]: 编译后的正则表达式
        """
        self.force_update()
        return self.get_regexs(slot_name, slot_value)
    
    def force_update_and_get_tool_regexs(self, tool_type: Optional[str] = None, tool_name: Optional[str] = None) -> Union[Dict[str, re.Pattern], re.Pattern, None]:
        """
        强制更新并获取工具类正则表达式
        
        Args:
            tool_type: 工具类型，如"amount"、"rate"等
            tool_name: 工具名称，如果指定则只返回特定工具的正则
            
        Returns:
            Union[Dict[str, re.Pattern], re.Pattern, None]: 编译后的工具类正则表达式
        """
        self.force_update()
        return self.get_tool_regexs(tool_type, tool_name)
    
    def get_last_update_time(self) -> Optional[datetime]:
        """
        获取上次缓存更新时间
        
        Returns:
            Optional[datetime]: 上次更新时间，如果未更新过则为None
        """
        with self._cache_lock:
            return self._last_update_time
    
    def is_running(self) -> bool:
        """
        查询缓存服务是否运行中
        
        Returns:
            bool: 服务是否运行
        """
        return self._running
    
    def force_update(self) -> bool:
        """
        强制更新缓存
        
        Returns:
            bool: 更新是否成功
        """
        logger.info("正在强制更新缓存...")
        return self._update_cache()

# 创建全局单例
cache_service = CacheService()
