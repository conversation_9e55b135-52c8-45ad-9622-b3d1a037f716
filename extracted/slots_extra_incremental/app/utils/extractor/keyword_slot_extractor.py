"""
基于关键词的槽位抽取器
负责从文本中提取基于关键词匹配或正则匹配的槽位信息

输入格式:
    text: str - 待提取的文本
    
输出格式:
    List[Dict[str, Any]] - 提取的槽位信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "value": str,            # 条件值名称
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值
        "position": Tuple[int, int]  # 在原文中的位置 (start, end)
    }
"""

from typing import Dict, List, Any, Tuple
from resource.slot_definitions import SLOTS
from app.services.cache_service import cache_service
from app.utils.extractor.shared.shared_utils import create_result, logger

class KeywordSlotExtractor:
    """
    基于关键词的槽位抽取器
    负责从文本中提取基于关键词匹配或正则匹配的槽位信息
    """
    
    def __init__(self):
        """
        初始化关键词槽位抽取器
        加载槽位定义
        """
        self.slots = SLOTS
        # 从缓存服务获取词库和正则模式的标志
        self._cache_initialized = False
        # R值到数值的映射字典
        self.r_value_mapping = {
            "R1": 1,
            "R2": 2,
            "R3": 3,
            "R4": 4,
            "R5": 5
        }
        self.r_slot_value = "specific_risk"
    
    def _ensure_cache_initialized(self) -> bool:
        """
        确保缓存已初始化，如果没有则尝试初始化
        
        Returns:
            bool: 缓存是否已初始化
        """
        if not self._cache_initialized:
            if not cache_service.is_running():
                logger.info("缓存服务未运行，正在启动...")
                if not cache_service.start():
                    logger.error("启动缓存服务失败，将使用预编译模式")
                    return False
            self._cache_initialized = True
        return True
    
    def _handle_special_values(self, value_name: str, is_bs_value: bool, slot_value: str) -> Tuple[str, str]:
        """
        处理特殊值（如R值）的转换
        
        Args:
            value_name: 槽位值名称
            is_bs_value: 是否为业务值
            slot_value: 原始槽位值
            
        Returns:
            Tuple[str, str]: 处理后的(value, slot_value)
        """
        # 根据is_bs_value设置value
        converted_value = value_name if is_bs_value else ""
        converted_slot_value = slot_value
        
        # 处理特殊的R值映射
        if value_name in self.r_value_mapping:
            converted_value = self.r_value_mapping[value_name]
            converted_slot_value = self.r_slot_value
            
        return converted_value, converted_slot_value
    
    def extract_keyword_slot(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取关键词槽位信息
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 槽位匹配信息列表
        """
        try:
            # 确保缓存已初始化
            if not self._ensure_cache_initialized():
                logger.warn("[KEYWORD_SLOT_EXTRACTOR] Warning: 缓存服务未正常启动，提取结果可能不完整")
            
            results = []
            
            # 获取编译后的关键词正则模式和专用正则模式
            keyword_patterns = cache_service.get_keywords()
            regex_patterns = cache_service.get_regexs()
            
            # 遍历SLOTS中的所有槽位和槽位值
            for slot_name, slot_info in self.slots.items():
                for slot_value, value_info in slot_info.get("values", {}).items():
                    # 获取槽位值名称和是否为业务值标志
                    value_name = value_info.get("name", "")
                    is_bs_value = value_info.get("is_bs_value", True)
                    
                    # 构建关键词和正则模式的键名
                    keyword_id = f"keywords_{slot_name}_{slot_value}"
                    regex_id = f"regexs_{slot_name}_{slot_value}"
                    
                    # 检查并处理关键词匹配
                    if keyword_id in keyword_patterns:
                        pattern = keyword_patterns[keyword_id]
                        for match in pattern.finditer(text):
                            match_text = match.group()
                            start, end = match.span()
                            # 处理特殊值
                            value, converted_slot_value = self._handle_special_values(
                                value_name, is_bs_value, slot_value
                            )
                            
                            # 创建标准结果
                            results.append(create_result(
                                match=match_text,
                                position=(start, end),
                                slot=slot_name,
                                slot_value=converted_slot_value,
                                value=value
                            ))
                    
                    # 检查并处理正则匹配
                    if regex_id in regex_patterns:
                        pattern = regex_patterns[regex_id]
                        for match in pattern.finditer(text):
                            match_text = match.group()
                            start, end = match.span()
                            
                            # 处理特殊值
                            value, converted_slot_value = self._handle_special_values(
                                value_name, is_bs_value, slot_value
                            )
                            
                            # 创建标准结果
                            results.append(create_result(
                                match=match_text,
                                position=(start, end),
                                slot=slot_name,
                                slot_value=converted_slot_value,
                                value=value
                            ))
            
            return results
            
        except Exception as e:
            logger.warn(f"[KEYWORD_SLOT_EXTRACTOR] Warning: {e}")
            return []
        