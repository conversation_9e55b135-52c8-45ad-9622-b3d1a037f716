"""
通用工具类和函数，用于所有抽取器共享
"""
import logging
import os
from typing import Dict, Tuple, Any, Optional, List, Set

# 配置日志系统
def setup_logger(name: str = None, 
                level: int = logging.INFO, 
                log_format: str = None,
                log_file: str = None) -> logging.Logger:
    """配置并返回一个日志记录器

    Args:
        name: 日志记录器名称，默认为根日志记录器
        level: 日志级别，默认为INFO
        log_format: 日志格式，默认为"%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        log_file: 日志文件路径，默认为None（仅控制台输出）

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 使用默认的格式化字符串
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复配置处理器
    if not logger.handlers:
        # 配置控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(log_format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 如果指定了日志文件，则添加文件处理器
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(level)
            file_formatter = logging.Formatter(log_format)
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
    
    return logger

# 创建默认日志记录器
logger = setup_logger('extractor', level=logging.INFO)

def create_result(
    match: str, 
    position: Tuple[int, int],
    slot: Optional[str],
    slot_value: Optional[str],
    value: Optional[float] = None,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
) -> Dict[str, Any]:
    """创建标准化的结果对象

    Args:
        match: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot: 槽位类型，如'time_range', 'number', '<keyword>'等
        slot_value: 槽位值，如'time_range', 'amount_range', 'specific_amount'等
        value: 具体数值，对于具体值类型结果
        min_value: 范围最小值，对于范围类型结果
        max_value: 范围最大值，对于范围类型结果

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    result = {
        "match": match,
        "position": position,
        "slot": slot,
        "slot_value": slot_value
    }
    
    if value is not None:
        result["value"] = value
    else:
        result["minValue"] = None
        result["maxValue"] = None
        
    if min_value is not None:
        result["minValue"] = min_value
    if max_value is not None:
        result["maxValue"] = max_value

    return result

def resolve_overlapping_results(results: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    重叠结果解决算法，返回非重叠的结果和被过滤掉的结果
    
    优先选择匹配长度最长的结果，如果长度相同则选择靠前的
    
    Args:
        results: 所有抽取器产生的结果列表，包含startPos和endPos字段
            
    Returns:
        Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: 
            (处理后的非重叠匹配结果, 被过滤掉的重叠结果)
    """
    if not results:
        return [], []
    
    # 确保所有结果都有position字段
    valid_results = []
    for result in results:
        if "startPos" in result and "endPos" in result:
            valid_results.append(result)
        else:
            logger.warn(f"跳过没有位置信息的结果: {result}")
            continue
    
    # 按匹配长度降序排序，相同长度按位置升序排序
    sorted_results = sorted(
        valid_results, 
        key=lambda x: (-(x["endPos"] - x["startPos"]), x["startPos"])
    )
    
    covered_positions = set()  # 记录已覆盖的位置
    non_overlapping_results = []
    filtered_results = []
    
    for result in sorted_results:
        start, end = result["startPos"], result["endPos"]
        
        # 检查是否与已选择的结果重叠
        overlap = False
        for pos in range(start, end):
            if pos in covered_positions:
                overlap = True
                break
        
        if not overlap:
            # 标记已覆盖的位置
            for pos in range(start, end):
                covered_positions.add(pos)
            non_overlapping_results.append(result)
        else:
            # 记录被过滤掉的结果
            filtered_results.append(result)
    
    return non_overlapping_results, filtered_results

def is_range_partially_overlapping(range1: Tuple[int, int], range2: Tuple[int, int]) -> bool:
    """
    判断两个范围是否部分重叠（非完全包含关系）
    
    Args:
        range1: 第一个范围 (start, end)
        range2: 第二个范围 (start, end)
        
    Returns:
        bool: 如果两个范围部分重叠而不是完全包含，返回True
    """
    start1, end1 = range1
    start2, end2 = range2
    
    # 检查是否有重叠
    if end1 <= start2 or end2 <= start1:
        return False  # 没有重叠
    
    # 检查是否是完全包含关系
    if (start1 <= start2 and end1 >= end2) or (start2 <= start1 and end2 >= end1):
        return False  # 完全包含
    
    # 部分重叠
    return True

def process_filtered_time_slots(
    non_overlapping_results: List[Dict[str, Any]], 
    filtered_results: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    处理被过滤掉的时间槽位，恢复特定条件下的时间槽位
    
    特殊处理逻辑：如果被过滤的时间槽位仅与"return_demand"槽位重叠，
    且为部分重叠（非完全包含关系），则恢复该时间槽位
    
    Args:
        non_overlapping_results: 去重后的非重叠槽位结果
        filtered_results: 被过滤掉的槽位结果
        
    Returns:
        List[Dict[str, Any]]: 需要恢复的时间槽位列表
    """
    # 筛选出被过滤的时间槽位
    filtered_time_slots = [
        result for result in filtered_results 
        if result.get("slot") == "time"
    ]
    
    # 如果没有被过滤的时间槽位，直接返回空列表
    if not filtered_time_slots:
        return []
    
    # 查找需要恢复的时间槽位
    slots_to_recover = []
    
    for time_slot in filtered_time_slots:
        time_range = (time_slot["startPos"], time_slot["endPos"])
        
        # 标记是否只与return_demand重叠且为部分重叠
        should_recover = True
        overlapped_with_return_demand = False
        
        for result in non_overlapping_results:
            result_range = (result["startPos"], result["endPos"])
            
            # 检查是否有重叠
            if not (time_range[1] <= result_range[0] or result_range[1] <= time_range[0]):
                # 有重叠，判断重叠类型和槽位类型
                if result.get("slot") == "return_demand":
                    # 如果是与return_demand重叠，检查是否是部分重叠
                    if is_range_partially_overlapping(time_range, result_range):
                        overlapped_with_return_demand = True
                    else:
                        # 完全包含关系，不应恢复
                        should_recover = False
                        break
                else:
                    # 与非return_demand槽位重叠，不应恢复
                    should_recover = False
                    break
        
        # 如果只与return_demand部分重叠，则恢复该时间槽位
        if should_recover and overlapped_with_return_demand:
            slots_to_recover.append(time_slot)
            logger.debug(f"恢复被过滤的时间槽位: {time_slot}")
    
    return slots_to_recover
