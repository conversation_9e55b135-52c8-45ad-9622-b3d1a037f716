"""
时间抽取相关常量和通用工具类
"""
from typing import Tuple, List
import re
from app.utils.extractor.num.number_convert import text_to_num
from resource.regex_patterns.num_patterns import (
    NUM_PREFIX, 
    COMPLEX_NUMBER_PATTERN
)
from app.utils.extractor.shared.shared_utils import logger
from resource.regex_patterns.time_duration_patterns import (
    DURATION_TIME_RANGE_PREFIX,
    TimeUnits,
    TIME_UNIT_PATTERN
)
from app.services.cache_service import cache_service

def get_regex(tool_name: str, default_regex: str, slot_name: str = DURATION_TIME_RANGE_PREFIX) -> re.Pattern:
        """获取正则表达式"""
        cached_regex = cache_service.get_tool_regexs(
            slot_name=slot_name,
            tool_name=tool_name
        )
        
        if not cached_regex:
            logger.warning(f"{tool_name} is None, use default pattern instead")
            # 如果默认模式是字符串则编译，已经是编译好的正则则直接使用
            return re.compile(default_regex) if isinstance(default_regex, str) else default_regex
        return cached_regex
    
class TimeUnitHandler:
    """时间单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化时间单位"""
        return TimeUnits.get_standard_unit(unit)
    
    @classmethod
    def to_days(cls, value: float, unit: str) -> float:
        """将时间单位转换为天数"""
        standard_unit = cls.standardize(unit)
        return value * TimeUnits.DAYS_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        USE_COMPLEX_NUMBER_PATTERN = get_regex(tool_name="complex_number", default_regex=COMPLEX_NUMBER_PATTERN, slot_name=NUM_PREFIX)
        USE_TIME_UNIT_PATTERN = get_regex(tool_name="time_unit_pattern", default_regex=TIME_UNIT_PATTERN)
        
        # 提取数字
        number_match = re.search(USE_COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = text_to_num(number_text)
        if number is None:
            return None, None
        
        # 提取单位
        unit_match = re.search(USE_TIME_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "天"
        
        # 处理"半"字
        if "半" in text:
            if text.startswith("半"):
                number = 0.5
            elif "半" in text and number > 0:
                number += 0.5
                
        return number, unit
    
    @classmethod
    def is_year_identifier(cls, text: str) -> bool:
        """
        判断文本是否为年份标识符（如2024年，2000年等）
        
        Args:
            text: 要检查的文本（假定包含单位）
            
        Returns:
            bool: 如果文本匹配年份标识符规则，则返回True
        """
        USE_COMPLEX_NUMBER_PATTERN = get_regex(tool_name="complex_number", default_regex=COMPLEX_NUMBER_PATTERN, slot_name=NUM_PREFIX)
        USE_TIME_UNIT_PATTERN = get_regex(tool_name="time_unit_pattern", default_regex=TIME_UNIT_PATTERN)
        
        # 检查单位
        unit_match = re.search(USE_TIME_UNIT_PATTERN, text)
        if not unit_match or unit_match.group(1) not in ["年", "年份"]:
            return False
        
        # 提取数字部分
        number_match = re.search(USE_COMPLEX_NUMBER_PATTERN, text)
        if not number_match:
            return False
        
        # 检查数值是否在年份范围内
        try:
            number = text_to_num(number_match.group(0))
            if number is None:
                return False
            return 1000 <= number <= 2100  # 合理的年份范围
        except:
            return False
        
class TimeModifierHandler:
    """时间修饰词处理器"""
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, has_upper: bool, has_lower: bool, has_negation: bool) -> Tuple[float, float]:
        """
        应用修饰词调整时间范围
        
        Args:
            min_value: 初始最小值（天数）
            max_value: 初始最大值（天数）
            has_upper: 是否存在上限修饰词
            has_lower: 是否存在下限修饰词
            has_negation: 是否存在否定前缀
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 处理否定前缀：如果有否定前缀，则取反上下限修饰词的影响
        if has_negation:
            # 如果有否定前缀，上限修饰词变为下限修饰词，下限修饰词变为上限修饰词
            has_upper, has_lower = has_lower, has_upper
  
        # 情况1: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_days = min_value
            
            # 下限修饰词：如"至少3个月"，表示最小值是3个月，最大值无限
            if has_lower:
                min_value = base_days
                max_value = None
            
            # 上限修饰词：如"不超过3个月"，表示最大值是3个月，最小值为0
            elif has_upper:
                min_value = 0
                max_value = base_days
                
        # 情况2: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少3-6个月"，表示最小值是6个月，最大值无限
            if has_lower:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过3-6个月"，表示最大值是6个月，最小值为0
            elif has_upper:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value
