"""
金融领域持续时间抽取器

专注于提取金融文本中的时间信息，抽取持续时间范围：

如"一年半"、"90天到180天"、"三年左右"等
    
特别说明：
    1. 一年按366天计算，一个月按30天计算
    2. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，
       如果只有单个时间跨度描述，则最小最大值为该描述转天数。
    3. 如果无法标准化为天数，则返回None。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""
from typing import Dict, List, Any, Optional, Tuple
import re
from app.utils.extractor.time.time_utils import (
    TimeUnitHandler, TimeModifierHandler
)
from app.utils.extractor.shared.shared_utils import logger
from app.utils.extractor.shared.shared_utils import create_result
from app.utils.extractor.num.number_convert import text_to_num
from app.services.cache_service import cache_service
from resource.regex_patterns.num_patterns import (
    NUM_PREFIX, 
    COMPLEX_NUMBER_PATTERN_COMPILED
)
from resource.regex_patterns.time_duration_patterns import (
    DURATION_TIME_RANGE_PREFIX,
    DURATION_SUPER_PATTERN_COMPILED,
    DURATION_RANGE_WITH_INFIX_PATTERN_COMPILED,  
    TERM_PATTERN_COMPILED,
    PERIOD_PATTERN_COMPILED,
    SIMPLE_TIME_PATTERN_COMPILED,
    COMPLEX_TIME_PATTERN_COMPILED,
    UNIT_HALF_PATTERN_COMPILED,
    HALF_UNIT_PATTERN_COMPILED,
    TIME_UNIT_PATTERN_COMPILED
)

class DurationTimeExtractor:
    """持续时间抽取器，用于提取文本中的持续时间表达（如"3个月"、"1-2年"等）"""
    NUM_TOOL_REGEXS_PREFIX = f"tool_regexs_{NUM_PREFIX}"
    DURATION_TIME_RANGE_TOOL_REGEXS_PREFIX = f"tool_regexs_{DURATION_TIME_RANGE_PREFIX}"
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取持续时间表达式"""
        results = []
        USE_DURATION_SUPER_PATTERN = self._get_regex(tool_name="super_pattern", default_regex=DURATION_SUPER_PATTERN_COMPILED)
        USE_DURATION_RANGE_WITH_INFIX_PATTERN = self._get_regex(tool_name="duration_range_with_infix_pattern", default_regex=DURATION_RANGE_WITH_INFIX_PATTERN_COMPILED)
        USE_TERM_PATTERN = self._get_regex(tool_name="term_pattern", default_regex=TERM_PATTERN_COMPILED)
        USE_PERIOD_PATTERN = self._get_regex(tool_name="period_pattern", default_regex=PERIOD_PATTERN_COMPILED)
        
        for match in USE_DURATION_SUPER_PATTERN.finditer(text):
            result = self.process_unified_duration(match)
            if result:
                results.append(result)
                    
        for match in USE_DURATION_RANGE_WITH_INFIX_PATTERN.finditer(text):
            result = self.process_infix_duration(match)
            if result:
                results.append(result)
        
        for match in USE_TERM_PATTERN.finditer(text):
            result = self.process_term(match)
            if result:
                results.append(result)
            
        for match in USE_PERIOD_PATTERN.finditer(text):
            result = self.process_period(match)
            if result:
                results.append(result)
                
        return results 

    def process_unified_duration(self, match) -> Optional[Dict[str, Any]]:
        """处理统一持续时间模式的匹配结果"""
        match_text = match.group(0)
     
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        # 处理不同类型的时间表达式并获取最小和最大值
        min_value, max_value = self._extract_duration_values(components)

        # 如果min_value和max_value都为None，则返回None
        if min_value is None and max_value is None:
            return None
        
        # 提取各类修饰词分组并进行判断
        has_upper = match.group('upper_limit_prefix') is not None or match.group('upper_limit_suffix') is not None
        has_lower = match.group('lower_limit_prefix') is not None or match.group('lower_limit_suffix') is not None
        has_negation = match.group('negation_prefix') is not None
        
        # 应用修饰词的影响，传递判断结果
        min_value, max_value = TimeModifierHandler.apply_modifiers(
            min_value=min_value, 
            max_value=max_value, 
            has_upper=has_upper,
            has_lower=has_lower,
            has_negation=has_negation
        )
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE_PREFIX,
            min_value=min_value,
            max_value=max_value,
        )
        
    def _extract_duration_values(self, components) -> Tuple[Optional[float], Optional[float]]:
        """从组件中提取持续时间的最小值和最大值"""
        days = None
        days1 = None
        days2 = None
        min_value = None
        max_value = None
        
        # 处理连接词形式的时间范围
        if 'time_unit1' in components and 'time_unit2' in components:
            days1 = self._process_time_unit(components['time_unit1'])
            days2 = self._process_time_unit(components['time_unit2'])

        # 处理单个时间单元
        elif 'time_unit' in components:
            days = self._process_time_unit(components['time_unit'])
            
        # 处理数字+连接词+数字+时间单元模式
        elif 'number1' in components and 'number2' in components and 'shared_unit' in components:
            days1, days2 = self._process_number_range_with_unit(
                components['number1'], 
                components['number2'], 
                components['shared_unit']
            )

        # min/max 调整
        if days is not None:
            min_value, max_value = days, days
        elif days1 is not None and days2 is not None:
            min_value = min(days1, days2)
            max_value = max(days1, days2)
        elif days1 is not None:
            min_value, max_value = days1, days1
        elif days2 is not None:
            min_value, max_value = days2, days2
            
        return min_value, max_value
        
    def process_infix_duration(self, match) -> Optional[Dict[str, Any]]:
        """处理带中缀的持续时间范围匹配（如"短则3天，长则7天"）"""
        match_text = match.group(0)
     
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        # 使用已解析的分组直接获取时间单元内容
        first_time_unit = components['first_time_unit']
        second_time_unit = components['second_time_unit']
        
        # 处理时间单元，得到对应的天数
        days1 = self._process_time_unit(first_time_unit)
        days2 = self._process_time_unit(second_time_unit)
        
        # 检查处理结果是否为None
        if days1 and days2:
            min_value = min(days1, days2) 
            max_value = max(days1, days2)
        elif days1:
            min_value, max_value = days1, days1
        elif days2:
            min_value, max_value = days2, days2
        else:
            return None
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE_PREFIX,
            min_value=min_value,
            max_value=max_value,
        )

    def process_term(self, match) -> Optional[Dict[str, Any]]:
        """处理短期/中期/长期表达式"""
        match_text = match.group(0)
        
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        
        # 直接使用具名分组判断类型，而不是再次进行正则匹配
        if components.get('short_term', None):
            # 短期: 0到30天
            min_value = 0.0
            max_value = 30.0
        elif components.get('middle_term', None):
            # 中期: 30到180天
            min_value = 30.0
            max_value = 180.0
        elif components.get('long_term', None):
            # 长期: 180天以上
            min_value = 180.0
            max_value = None  # None 表示无上限
        else:
            return None
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE_PREFIX,
            min_value=min_value,
            max_value=max_value,
        )

    def process_period(self, match) -> Optional[Dict[str, Any]]:
        """处理周期性时间单位表达式（季度、周度、月度等）
        
        Args:
            match: 正则匹配结果

        Returns:
            解析结果字典或None
        """
        match_text = match.group(0)
        
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        # 直接使用具名分组来确定周期类型
        if components.get('week_period', None):
            days = 7.0  # 周度: 7天
        elif components.get('month_period', None):
            days = 30.0  # 月度: 30天
        elif components.get('quarter_period', None):
            days = 90.0  # 季度: 90天
        elif components.get('year_period', None):
            days = 366.0  # 年度: 366天
        else:
            return None
        
        min_value, max_value = days, days
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE_PREFIX,
            min_value=min_value,
            max_value=max_value,
        )

    def _process_time_unit(self, text: str) -> Optional[float]:
        """处理通用时间单元表达式，返回对应的天数，如果是年份标识符则返回None"""
        # 首先检查是否为年份标识符
        if TimeUnitHandler.is_year_identifier(text) or "近" in text:
            return None
        
        # 使用具体模式匹配而不是依赖命名分组
        USE_COMPLEX_TIME_PATTERN = self._get_regex(tool_name="complex_time_pattern", default_regex=COMPLEX_TIME_PATTERN_COMPILED)
        USE_UNIT_HALF_PATTERN = self._get_regex(tool_name="unit_half_pattern", default_regex=UNIT_HALF_PATTERN_COMPILED)
        USE_HALF_UNIT_PATTERN = self._get_regex(tool_name="half_unit_pattern", default_regex=HALF_UNIT_PATTERN_COMPILED)
        
        # 依次检查各种时间单元模式
        # 1. 复合时间模式 (如 "1年3个月")
        complex_time_pattern = USE_COMPLEX_TIME_PATTERN.search(text)
        if complex_time_pattern:
            return self._process_compound_expression(text)

        # 2. 单位加半模式 (如 "一年半")
        unit_half_pattern = USE_UNIT_HALF_PATTERN.search(text)
        if unit_half_pattern:
            return self._process_unit_half_expression(text)
            
        # 3. 半单位模式 (如 "半年")
        half_unit_pattern = USE_HALF_UNIT_PATTERN.search(text)
        if half_unit_pattern:
            return self._process_half_unit_expression(text)
            
        # 4. 简单时间单位 (数字+单位)
        # 如果不是上面三种模式，则尝试作为简单时间单位处理
        number, unit = TimeUnitHandler.parse_number_and_unit(text)
        if number is not None and unit is not None:
            return TimeUnitHandler.to_days(number, unit)
        
        return None

    def _process_compound_expression(self, text: str) -> float:
        """处理复合时间表达式（如"1年3个月"或"1年零3个月"）"""
        total_days = 0
        USE_SIMPLE_TIME_PATTERN = self._get_regex(tool_name="simple_time_pattern", default_regex=SIMPLE_TIME_PATTERN_COMPILED)
        USE_COMPLEX_NUMBER_PATTERN = self._get_regex(tool_name="complex_number", default_regex=COMPLEX_NUMBER_PATTERN_COMPILED, slot_name=NUM_PREFIX)
        USE_TIME_UNIT_PATTERN = self._get_regex(tool_name="time_unit_pattern", default_regex=TIME_UNIT_PATTERN_COMPILED)
        
        simple_time_matches = list(USE_SIMPLE_TIME_PATTERN.finditer(text))
        
        for simple_time_match in simple_time_matches:
            num_match = USE_COMPLEX_NUMBER_PATTERN.search(simple_time_match.group(0))
            unit_match = USE_TIME_UNIT_PATTERN.search(simple_time_match.group(0))
            if not num_match or not unit_match:
                continue
            num = text_to_num(num_match.group(0))
            if num is None:
                continue
            unit = TimeUnitHandler.standardize(unit_match.group(0))
            total_days += TimeUnitHandler.to_days(num, unit)
            
        return total_days

    def _process_unit_half_expression(self, text: str) -> float:
        """处理单位加半表达式（如"一年半"、"两月半"）"""
        USE_COMPLEX_NUMBER_PATTERN = self._get_regex(tool_name="complex_number", default_regex=COMPLEX_NUMBER_PATTERN_COMPILED, slot_name=NUM_PREFIX)
        USE_TIME_UNIT_PATTERN = self._get_regex(tool_name="time_unit_pattern", default_regex=TIME_UNIT_PATTERN_COMPILED)
        
        number_match = USE_COMPLEX_NUMBER_PATTERN.search(text)
        unit_match = USE_TIME_UNIT_PATTERN.search(text)

        if not number_match or not unit_match:
            return 0
        
        number = text_to_num(number_match.group(1))
        if number is None:  # 检查number是否为None
            return 0
            
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算基础天数
        base_days = TimeUnitHandler.to_days(number, unit)
        
        # 计算"半"的天数（单位的一半）
        half_days = TimeUnitHandler.to_days(0.5, unit)
        
        # 返回总天数
        return base_days + half_days
        
    def _process_half_unit_expression(self, text: str) -> float:
        """处理半单位表达式（如"半年"、"半个月"）"""
        USE_TIME_UNIT_PATTERN = self._get_regex(tool_name="time_unit_pattern", default_regex=TIME_UNIT_PATTERN_COMPILED)
     
        # 提取单位
        unit_match = USE_TIME_UNIT_PATTERN.search(text)
        
        if not unit_match:
            return 0
        
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算半个单位的天数
        return TimeUnitHandler.to_days(0.5, unit)

    def _process_number_range_with_unit(self, number1_str: str, number2_str: str, unit: str) -> Tuple[Optional[float], Optional[float]]:
        """处理数字范围和共享单位的情况"""
        number1 = text_to_num(number1_str)
        number2 = text_to_num(number2_str)

        # 检查转换结果是否为None
        if number1 is None and number2 is None:
            return None, None
        elif number1 is None:
            number1 = number2
        elif number2 is None:
            number2 = number1

        unit = TimeUnitHandler.standardize(unit)
        
        # 检查是否有年份标识符
        if (unit in ["年", "年份"] and (number1 >= 1000 or number2 >= 1000)):
            return None, None
        
        days1 = TimeUnitHandler.to_days(number1, unit)
        days2 = TimeUnitHandler.to_days(number2, unit)
        
        return min(days1, days2), max(days1, days2)

    def _get_regex(self, tool_name: str, default_regex: str, slot_name: str = DURATION_TIME_RANGE_PREFIX) -> re.Pattern:
        """获取正则表达式"""
        cached_regex = cache_service.get_tool_regexs(
            slot_name=slot_name,
            tool_name=tool_name
        )
        
        if not cached_regex:
            logger.warning(f"{tool_name} is None, use default pattern instead")
            # 如果默认模式是字符串则编译，已经是编译好的正则则直接使用
            return re.compile(default_regex) if isinstance(default_regex, str) else default_regex
        return cached_regex
