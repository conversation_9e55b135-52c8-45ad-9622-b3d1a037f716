"""
槽位定义文件
包含所有槽位和对应条件值的定义
"""

# 槽位定义
SLOTS = {
    "time": {
        "name": "时间期限",
        "description": "用于表示时间范围，包括相对时间范围和持续时间范围",
        "values": {
            "relative_time_range": {
                "is_bs_value": False,
                "name": "相对时间范围",
                "description": "相对于当前时间的时间范围，用于年化收益率，如近六月到近三月，统一单位为天"
            },
            "duration_time_range": {
                "is_bs_value": False,
                "name": "持续时间范围",
                "description": "持续时间的时间范围，用于投资期限，如三个月到六个月，统一单位为天"
            },
        }
    },
    "number": {
        "name": "数值",
        "description": "用于表示产品的起购金额、收益率等数值",
        "values": {
            "amount_range": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "金额范围",
                "description": "匹配有明确金额单位的数值范围，包括最小金额～最大金额"
            },
            "specific_amount": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "具体金额",
                "description": "匹配具体金额，并将自然语言描述金额转为具体单位元"
            },
            "rate_range": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "收益率范围",
                "description": "匹配没有金额单位的收益率范围，包括最小收益率～最大收益率"
            },
            "specific_rate": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "具体收益率",
                "description": "匹配具体收益率，并将自然语言描述收益率转为具体单位百分数"
            }
        }
    },
    "risk_level": {
        "name": "风险等级",
        "description": "用于表示产品的风险等级",
        "values": {
            "specific_risk": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "具体风险等级",
                "description": "匹配具体风险等级，如R1、R2、R3、R4、R5，并转为具体风险等级1、2、3、4、5"
            },
            "risk_range": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "风险等级范围",
                "description": "匹配风险等级范围，如R1-R2、R2-R3、R3-R4、R4-R5，并转为具体风险等级范围"
            },
        }
    },
    "return_demand": {
        "name": "收益需求",
        "description": "用于表示收益的需求描述",
        "values": {
            "annual_return_demand": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "年化收益率",
                "description": "年化收益率、收益、年华等"
            },
            "interest_start_demand": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "起息",
                "description": "起息计算收益的、用于表示产品开始计算收益的时间点的条件，如T+0起息、T+1起息等"
            },
            "daily_positive": {
                "name": "历史日日正收益",
                "description": "历史日日正收益"
            },
            "weekly_positive": {
                "name": "历史周周正收益",
                "description": "历史周周正收益"
            },
            "monthly_positive": {
                "name": "历史月月正收益",
                "description": "历史月月正收益"
            },
            "quarterly_positive": {
                "name": "历史季季正收益",
                "description": "历史季季正收益"
            },
            "yearly_positive": {
                "name": "历史年年正收益",
                "description": "历史年年正收益"
            },
            "period_positive": {
                "name": "历史期期正收益",
                "description": "历史期期正收益"
            }
        }
    },
    "specific_product": {
        "name": "产品名称",
        "description": "用于表示具体的产品名称或类型",
        "values": {
            "weekly_bao": {
                "name": "周周宝",
                "description": "周周宝产品"
            },
            "monthly_bao": {
                "name": "月月宝",
                "description": "月月宝产品"
            },
            "quarterly_bao": {
                "name": "季季宝",
                "description": "季季宝产品"
            },
            "half_year_bao": {
                "name": "半年宝",
                "description": "半年宝产品"
            },
            "multi_month_bao": {
                "name": "多月宝",
                "description": "多月宝产品"
            },
            "fixed_bao": {
                "name": "定期宝",
                "description": "定期宝产品"
            },
            "value_plus": {
                "name": "价值+",
                "description": "价值+产品"
            },
            "multi_plus": {
                "name": "多元+",
                "description": "多元+产品"
            },
            "quant_plus": {
                "name": "量化+",
                "description": "量化+产品"
            },
            "dividend_plus": {
                "name": "红利+",
                "description": "红利+产品"
            },
            "gold_plus": {
                "name": "黄金+",
                "description": "黄金+产品"
            },
            "global_plus": {
                "name": "全球+",
                "description": "全球+产品"
            },
            "structure_plus": {
                "name": "结构+",
                "description": "结构+产品"
            },
            "you_plus": {
                "name": "优+",
                "description": "优+产品"
            },
            "fast_redemption_zone": {
                "name": "快赎专区",
                "description": "快赎专区产品"
            },
            "daily_bao": {
                "name": "朝朝宝",
                "description": "朝朝宝产品"
            },
            "idle_cash_management": {
                "is_bs_value": False,
                "name": "闲钱理财",
                "description": "闲钱理财产品、提供7天闲钱理财、30天闲钱理财、90天闲钱理财条件使用"
            },
        }
    },
    "product_type": {
        "name": "产品形态",
        "description": "用于表示产品的运作方式",
        "values": {
            "closed_end": {
                "name": "封闭型",
                "description": "封闭型产品"
            },
            "regularly_open": {
                "name": "定期开放",
                "description": "定期开放产品"
            },
            "minimum_holding": {
                "name": "最短持有期",
                "description": "最短持有期产品"
            }
        }
    },
    "start_amount": {
        "name": "起购金额",
        "description": "用于表示产品的起购金额",
        "values": {
            "under_1000": {
                "name": "1千以下",
                "description": "1000元以下"
            },
            "1000_50000": {
                "name": "1千-5万",
                "description": "1000元-50000元"
            },
            "above_50000": {
                "name": "5万以上",
                "description": "50000元以上"
            }
        }
    },
    "raise_type": {
        "name": "募集方式",
        "description": "用于表示产品的募集方式",
        "values": {
            "public": {
                "name": "公募理财",
                "description": "公募理财产品"
            },
            "private": {
                "name": "私募理财",
                "description": "私募理财产品"
            }
        }
    },
    "investment_strategy": {
        "name": "投资策略",
        "description": "用于表示产品的投资策略",
        "values": {
            "cash_management": {
                "name": "活钱管理",
                "description": "活钱管理策略"
            },
            "stable_low_vol": {
                "name": "稳健低波",
                "description": "稳健低波动策略"
            },
            "stable_growth": {
                "name": "稳健增值",
                "description": "稳健增值策略"
            },
            "balanced_growth": {
                "name": "稳中求进",
                "description": "稳中求进策略"
            },
            "aggressive": {
                "name": "进取投资",
                "description": "进取投资策略"
            }
        }
    },
    "investment_direction": {
        "name": "投资方向",
        "description": "用于表示产品的投资标的",
        "values": {
            "fixed_income": {
                "name": "固定收益类",
                "description": "固定收益类资产"
            },
            "equity": {
                "name": "权益类资产",
                "description": "权益类资产"
            },
            "commodity": {
                "name": "商品及金融衍生品类资产",
                "description": "商品及金融衍生品类资产"
            },
            "cash": {
                "name": "现金类资产",
                "description": "现金类资产"
            },
            "mixed": {
                "name": "混合类资产",
                "description": "混合类资产"
            },
            "foreign_currency": {
                "name": "外币资产",
                "description": "外币类资产"
            },
            "foreign_currency_usd": {
                "name": "外币资产-美元",
                "description": "外币资产-美元"
            },
            "foreign_currency_hkd": {
                "name": "外币资产-港元",
                "description": "外币资产-港元"
            },
            "foreign_currency_eur": {
                "name": "外币资产-欧元",
                "description": "外币资产-欧元"
            },
            "foreign_currency_gbp": {
                "name": "外币资产-英镑",
                "description": "外币资产-英镑"
            },
            "foreign_currency_aud": {
                "name": "外币资产-澳元",
                "description": "外币资产-澳元"
            },
            "foreign_currency_sgd": {
                "name": "外币资产-新元",
                "description": "外币资产-新元"
            }
        }
    },
    "special_label": {
        "name": "其他标签",
        "description": "用于表示产品的特殊标签",
        "values": {
            "high_popularity": {
                "name": "高人气",
                "description": "高人气产品"
            },
            "low_drawdown": {
                "name": "历史回撤小",
                "description": "历史回撤小"
            },
            "early_benefit": {
                "name": "早一天享收益",
                "description": "早一天享收益"
            },
            "anti_decline": {
                "name": "抗跌表现好",
                "description": "抗跌表现好"
            },
            "long_term_finance": {
                "name": "长盈理财",
                "description": "长盈理财"
            },
            "locked_coupon": {
                "name": "锁定票息",
                "description": "锁定票息"
            },
            "fixed_plus_reits": {
                "name": "固收+公募REITs",
                "description": "固收+公募REITs"
            },
            "fixed_plus_structured": {
                "name": "固收+结构化",
                "description": "固收+结构化"
            },
            "absolute_return": {
                "name": "绝对收益",
                "description": "绝对收益"
            },
            "fixed_plus_high_dividend": {
                "name": "固收+高股息",
                "description": "固收+高股息"
            },
            "maturity_mismatch": {
                "name": "期限错配",
                "description": "期限错配"
            },
            "stable_dividend": {
                "name": "稳定分红",
                "description": "稳定分红"
            },
            "long_term_selection": {
                "name": "长钱优选",
                "description": "长钱优选"
            },
            "selected_assets": {
                "name": "精选资产",
                "description": "精选资产"
            },
            "target_profit": {
                "name": "目标止盈",
                "description": "目标止盈"
            },
            "fixed_plus_preferred": {
                "name": "固收+优先股",
                "description": "固收+优先股"
            },
            "quantitative_neutral": {
                "name": "量化中性",
                "description": "量化中性"
            },
            "low_vol_fixed_plus": {
                "name": "低波固收＋",
                "description": "低波固收＋"
            },
            "value_added_selection": {
                "name": "增值优选",
                "description": "增值优选"
            },
            "convertible_bond": {
                "name": "可转债",
                "description": "可转债"
            },
            "private_banking": {
                "name": "私行尊享",
                "description": "私行尊享"
            },
            "fast_redemption": {
                "name": "赎回到账快",
                "description": "赎回到账快"
            },
            "over_hundred_thousand_holders": {
                "name": "此系列超十万人持有",
                "description": "此系列超十万人持有"
            },
            "over_ten_thousand_repurchases": {
                "name": "此系列超万人复购",
                "description": "此系列超万人复购"
            },
            "support_regular_investment": {
                "name": "支持定投",
                "description": "支持定投"
            },
            "recent_week_hot_search": {
                "name": "近一周热搜",
                "description": "近一周热搜"
            },
            "latest_release": {
                "name": "最新发售",
                "description": "最新发售"
            },
            "recent_week_over_ten_thousand_views": {
                "name": "近一周超万人浏览",
                "description": "近一周超万人浏览"
            },
            "all_periods_reached_benchmark": {
                "name": "往期收益均达基准",
                "description": "往期收益均达基准"
            },
            "consecutive_20_periods_reached_benchmark": {
                "name": "连续20期收益达基准",
                "description": "连续20期收益达基准"
            },
            "consecutive_15_periods_reached_benchmark": {
                "name": "连续15期收益达基准",
                "description": "连续15期收益达基准"
            },
            "consecutive_10_periods_reached_benchmark": {
                "name": "连续10期收益达基准",
                "description": "连续10期收益达基准"
            },
            "consecutive_9_periods_reached_benchmark": {
                "name": "连续9期收益达基准",
                "description": "连续9期收益达基准"
            },
            "consecutive_8_periods_reached_benchmark": {
                "name": "连续8期收益达基准",
                "description": "连续8期收益达基准"
            },
            "consecutive_7_periods_reached_benchmark": {    
                "name": "连续7期收益达基准",
                "description": "连续7期收益达基准"
            },
            "consecutive_6_periods_reached_benchmark": {
                "name": "连续6期收益达基准",
                "description": "连续6期收益达基准"
            },
            "consecutive_5_periods_reached_benchmark": {
                "name": "连续5期收益达基准",
                "description": "连续5期收益达基准"
            },
            "consecutive_4_periods_reached_benchmark": {
                "name": "连续4期收益达基准",
                "description": "连续4期收益达基准"
            },
            "consecutive_3_periods_reached_benchmark": {
                "name": "连续3期收益达基准",
                "description": "连续3期收益达基准"
            },
            "all_periods_exceeded_benchmark": {
                "name": "往期收益均超基准",
                "description": "往期收益均超基准"
            },
            "consecutive_20_periods_exceeded_benchmark": {
                "name": "连续20期收益超基准",
                "description": "连续20期收益超基准"
            },
            "consecutive_10_periods_exceeded_benchmark": {
                "name": "连续10期收益超基准",
                "description": "连续10期收益超基准"
            },
            "consecutive_9_periods_exceeded_benchmark": {
                "name": "连续9期收益超基准",
                "description": "连续9期收益超基准"
            },
            "consecutive_8_periods_exceeded_benchmark": {
                "name": "连续8期收益超基准",
                "description": "连续8期收益超基准"
            },
            "consecutive_7_periods_exceeded_benchmark": {
                "name": "连续7期收益超基准",
                "description": "连续7期收益超基准"
            },
            "consecutive_6_periods_exceeded_benchmark": {
                "name": "连续6期收益超基准",
                "description": "连续6期收益超基准"
            },
            "consecutive_5_periods_exceeded_benchmark": {
                "name": "连续5期收益超基准",
                "description": "连续5期收益超基准"
            },
            "consecutive_4_periods_exceeded_benchmark": {
                "name": "连续4期收益超基准",
                "description": "连续4期收益超基准"
            },
            "consecutive_3_periods_exceeded_benchmark": {
                "name": "连续3期收益超基准",
                "description": "连续3期收益超基准"
            },
            "consecutive_20_periods_positive": {
                "name": "连续20期正收益",
                "description": "连续20期正收益"
            },
            "consecutive_15_periods_positive": {
                "name": "连续15期正收益",
                "description": "连续15期正收益"
            },
            "consecutive_10_periods_positive": {
                "name": "连续10期正收益",
                "description": "连续10期正收益"
            },
            "consecutive_5_periods_positive": {
                "name": "连续5期正收益",
                "description": "连续5期正收益"
            }
            
        }
    }
} 