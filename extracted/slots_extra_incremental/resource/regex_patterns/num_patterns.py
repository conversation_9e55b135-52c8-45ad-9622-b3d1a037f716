import re

NUM_PREFIX = "num"

# 基础中文数字映射
BASE_DIGIT_MAP = {
    '零': 0, '〇': 0, 
    '一': 1,
    '二': 2, 
    '三': 3, 
    '四': 4, 
    '五': 5, 
    '六': 6,
    '七': 7,
    '八': 8,
    '九': 9, 
}

# 中文数字单位映射
UNIT_MAP = {
    '十': 10,
    '百': 100,
    '千': 1000, 
    '万': 10000, 
    '亿': 100000000, 
}

# 繁体数字和特殊形式映射到标准中文数字
TRADITIONAL_MAP = {
    # 数字
    '壹': "一",
    '贰': "二", '兩': "二", '两': "二", '双': "二", '貳': "二",
    '叁': "三", '叄': "三",
    '肆': "四",
    '伍': "五",
    '陆': "六", '陸': "六",
    '柒': "七",
    '捌': "八",
    '玖': "九",
    # 单位
    '拾': "十",
    '佰': "百",
    '仟': "千", 'K': "千", 'k': "千",
    '萬': "万", 'W': "万", 'w': "万",
    '億': "亿",
}

# 小数点字符集合
DECIMAL_POINTS = {'点', '點'}

# 中文数字字符集
CN_CHARS = "".join(set(list(BASE_DIGIT_MAP.keys()) + list(UNIT_MAP.keys()) + list(TRADITIONAL_MAP.keys()) + list(DECIMAL_POINTS)))

COMPLEX_NUMBER_PATTERN = rf'[{CN_CHARS}0-9.分之\s]+'

COMPLEX_NUMBER_PATTERN_COMPILED = re.compile(COMPLEX_NUMBER_PATTERN)

COMPILED_NUM_PATTERNS = {
    f"{NUM_PREFIX}-complex_number": COMPLEX_NUMBER_PATTERN_COMPILED,
}

if __name__ == "__main__":
    import sys
    import os

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_NUM_PATTERNS, "tool_regexs") 