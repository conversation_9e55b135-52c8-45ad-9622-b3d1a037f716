"""
理财咨询场景槽位的正则表达式模式

本文件包含理财咨询场景中各种槽位的正则表达式模式，用于文本匹配和槽位提取。
"""

import re

SLOT_NAME = "special_label"

# 数字映射（阿拉伯数字到中文数字）
NUM_MAPPING = {
    "3": "三",
    "4": "四",
    "5": "五",
    "6": "六",
    "7": "七",
    "8": "八",
    "9": "九",
    "10": "十",
    "15": "十五",
    "20": "二十"
}

# 连续期相关模式组件
CONSECUTIVE_PATTERN = r"(连续|持续|接连|连着|一连|不间断|持续性|连贯|不断)"
PERFORMANCE_WORDS = r"(收益|业绩|表现|回报|收入|盈利|利润)"

# 达到基准相关模式组件
REACH_VERBS = r"(达|符|达到|达标|符合|满足|到达|匹配|切合)"
BENCHMARK_WORDS = r"(基准|标准|指标|水平|参考|基准水平|基准收益|参考值|目标|预期|预定目标)"

# 超过基准相关模式组件
EXCEED_VERBS = r"(超|超过|超级|超出|胜过|胜|赢过|赢|优于|远超|跑赢|领先|高于|好于)"
EXCEED_PERF = r"(超标|表现突出|表现优异|业绩突出|收益突出|大幅领先)"

# 正收益相关模式组件
POSITIVE_WORDS = r"(正收益|正回报|正向收益|收益为正|业绩为正|表现为正|保持正收益|有盈利|盈利为正)"
ALL_WORDS = r"(都|全部|均|总是|一直|始终|每期|每次)"
STATE_VERBS = r"(为|是|保持|维持|处于|呈现)"
POSITIVE_STATE = r"(正|正向|正数|正值|正面|盈利|挣钱|赚钱)"
OBTAIN_VERBS = r"(有|获得|保持|实现|取得|创造)"
GOOD_PERF = r"(收益不错|收益良好|收益稳定|表现良好|表现稳定|业绩良好|回报可观)"

# 历史/过往相关模式组件
HISTORY_WORDS = r"(历史|过往|往期|之前|以前|历来|一直|始终|每期|每次|所有|全部|均|都)"

# 连续期达到基准模式
def get_consecutive_reached_benchmark_pattern(num: str) -> str:
    """
    获取连续期达到基准的正则表达式模式
    
    Args:
        num: 期数（阿拉伯数字）
        
    Returns:
        str: 正则表达式模式
    """
    chinese_num = NUM_MAPPING.get(num, num)
    period_pattern = rf"({num}|{chinese_num})\s*期"
    
    return rf"{CONSECUTIVE_PATTERN}\s*{period_pattern}\s*((({PERFORMANCE_WORDS}\s*)?)({REACH_VERBS}\s*{BENCHMARK_WORDS}))"

# 连续期超过基准模式
def get_consecutive_exceeded_benchmark_pattern(num: str) -> str:
    """
    获取连续期超过基准的正则表达式模式
    
    Args:
        num: 期数（阿拉伯数字）
        
    Returns:
        str: 正则表达式模式
    """
    chinese_num = NUM_MAPPING.get(num, num)
    period_pattern = rf"({num}|{chinese_num})\s*期"
    
    return rf"{CONSECUTIVE_PATTERN}\s*{period_pattern}\s*((({PERFORMANCE_WORDS}\s*)?)({EXCEED_VERBS}\s*{BENCHMARK_WORDS}|{EXCEED_PERF}))"

# 连续期正收益模式
def get_consecutive_positive_pattern(num: str) -> str:
    """
    获取连续期正收益的正则表达式模式
    
    Args:
        num: 期数（阿拉伯数字）
        
    Returns:
        str: 正则表达式模式
    """
    chinese_num = NUM_MAPPING.get(num, num)
    period_pattern = rf"({num}|{chinese_num})\s*期"
    
    return rf"{CONSECUTIVE_PATTERN}\s*{period_pattern}\s*({ALL_WORDS}?\s*{OBTAIN_VERBS}?\s*{POSITIVE_WORDS}|{GOOD_PERF}|{PERFORMANCE_WORDS}\s*{ALL_WORDS}?\s*{STATE_VERBS}?\s*{POSITIVE_STATE})"

# 往期达到基准模式
ALL_PERIODS_REACHED_BENCHMARK_PATTERN = rf"{HISTORY_WORDS}\s*((({PERFORMANCE_WORDS}\s*)?)({REACH_VERBS}\s*{BENCHMARK_WORDS}))"

# 往期超过基准模式
ALL_PERIODS_EXCEEDED_BENCHMARK_PATTERN = rf"""
    (?:
        (?:{HISTORY_WORDS}\s*)?
        (?:{PERFORMANCE_WORDS}\s*)?
        (?:{EXCEED_VERBS}\s*{BENCHMARK_WORDS}|{EXCEED_PERF})
    )
"""

# 往期正收益模式
ALL_PERIODS_POSITIVE_PATTERN = rf"{HISTORY_WORDS}\s*({ALL_WORDS}?\s*{OBTAIN_VERBS}?\s*{POSITIVE_WORDS}|{GOOD_PERF}|{PERFORMANCE_WORDS}\s*{ALL_WORDS}?\s*{STATE_VERBS}?\s*{POSITIVE_STATE})"

# 预编译的正则表达式
COMPILED_CONSECUTIVE_PERIODS_PATTERNS = {
    # 连续期达到基准
    f"{SLOT_NAME}-consecutive_3_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("3")),
    f"{SLOT_NAME}-consecutive_4_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("4")),
    f"{SLOT_NAME}-consecutive_5_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("5")),
    f"{SLOT_NAME}-consecutive_6_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("6")),
    f"{SLOT_NAME}-consecutive_7_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("7")),
    f"{SLOT_NAME}-consecutive_8_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("8")),
    f"{SLOT_NAME}-consecutive_9_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("9")),
    f"{SLOT_NAME}-consecutive_10_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("10")),
    f"{SLOT_NAME}-consecutive_15_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("15")),
    f"{SLOT_NAME}-consecutive_20_periods_reached_benchmark": re.compile(get_consecutive_reached_benchmark_pattern("20")),
    
    # 连续期超过基准
    f"{SLOT_NAME}-consecutive_3_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("3")),
    f"{SLOT_NAME}-consecutive_4_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("4")),
    f"{SLOT_NAME}-consecutive_5_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("5")),
    f"{SLOT_NAME}-consecutive_6_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("6")),
    f"{SLOT_NAME}-consecutive_7_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("7")),
    f"{SLOT_NAME}-consecutive_8_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("8")),
    f"{SLOT_NAME}-consecutive_9_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("9")),
    f"{SLOT_NAME}-consecutive_10_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("10")),
    f"{SLOT_NAME}-consecutive_15_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("15")),
    f"{SLOT_NAME}-consecutive_20_periods_exceeded_benchmark": re.compile(get_consecutive_exceeded_benchmark_pattern("20")),
    
    # 连续期正收益
    f"{SLOT_NAME}-consecutive_3_periods_positive": re.compile(get_consecutive_positive_pattern("3")),
    f"{SLOT_NAME}-consecutive_4_periods_positive": re.compile(get_consecutive_positive_pattern("4")),
    f"{SLOT_NAME}-consecutive_5_periods_positive": re.compile(get_consecutive_positive_pattern("5")),
    f"{SLOT_NAME}-consecutive_6_periods_positive": re.compile(get_consecutive_positive_pattern("6")),
    f"{SLOT_NAME}-consecutive_7_periods_positive": re.compile(get_consecutive_positive_pattern("7")),
    f"{SLOT_NAME}-consecutive_8_periods_positive": re.compile(get_consecutive_positive_pattern("8")),
    f"{SLOT_NAME}-consecutive_9_periods_positive": re.compile(get_consecutive_positive_pattern("9")),
    f"{SLOT_NAME}-consecutive_10_periods_positive": re.compile(get_consecutive_positive_pattern("10")),
    f"{SLOT_NAME}-consecutive_15_periods_positive": re.compile(get_consecutive_positive_pattern("15")),
    f"{SLOT_NAME}-consecutive_20_periods_positive": re.compile(get_consecutive_positive_pattern("20")),
    
    # 往期达到基准
    f"{SLOT_NAME}-all_periods_reached_benchmark": re.compile(ALL_PERIODS_REACHED_BENCHMARK_PATTERN),
    
    # 往期超过基准
    f"{SLOT_NAME}-all_periods_exceeded_benchmark": re.compile(ALL_PERIODS_EXCEEDED_BENCHMARK_PATTERN),
    
    # 往期正收益
    f"{SLOT_NAME}-all_periods_positive": re.compile(ALL_PERIODS_POSITIVE_PATTERN)
}

if __name__ == "__main__":
    import sys
    import os

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_CONSECUTIVE_PERIODS_PATTERNS, "tool_regexs")