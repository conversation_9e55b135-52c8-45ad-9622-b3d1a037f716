import re
import os
import sys
from typing import List

AMOUNT_PREFIX = "amount"

if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from resource.regex_patterns.num_patterns import COMPLEX_NUMBER_PATTERN

# 金额单位定义
class AmountUnits:
    """金额单位定义类"""
    
    # 金额单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 小额
        "分": "分", "角": "角", "毛": "角",
        # 人民币基本单位
        "元": "元", "块": "元", "人民币": "元", "rmb": "元", "RMB": "元", "￥": "元",
        # 美元
        "美元": "美元", "美钞": "美元", "usd": "美元", "USD": "美元", "$": "美元", "刀": "美元", 
        # 港币
        "港币": "港币", "hkd": "港币", "HKD": "港币", "HK$": "港币",  
        # 欧元
        "欧元": "欧元", "eur": "欧元", "EUR": "欧元", "€": "欧元", "欧": "欧元", 
        # 英镑
        "英镑": "英镑", "gbp": "英镑", "GBP": "英镑", "£": "英镑", "镑": "英镑", 
        # 澳元
        "澳元": "澳元", "aud": "澳元", "AUD": "澳元", 
        # 新元
        "新元": "新元", "sgd": "新元", "SGD": "新元", "S$": "新元", 
    }
    
    # 金额单位转换为元的比例
    YUAN_CONVERSION = {
        "元": 1,
        "角": 0.1,
        "分": 0.01,
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有金额单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的金额单位"""
        return cls.UNIT_MAPPING.get(unit, unit)
    
    @classmethod
    def get_pattern(cls) -> str:
        """获取金额单位的正则表达式模式"""
        # 转义特殊字符
        units = []
        for unit in cls.get_all_units():
            # 转义正则表达式特殊字符: $ . ^ { [ ( | ) * + ? \ 
            if any(c in r'$.|*+?()[]{}^' for c in unit):
                escaped_unit = ''
                for c in unit:
                    if c in r'$.|*+?()[]{}^':
                        escaped_unit += '\\' + c
                    else:
                        escaped_unit += c
                units.append(escaped_unit)
            else:
                units.append(unit)
        
        return f"({'|'.join(units)})"

# 金额单位模式
AMOUNT_UNIT_PATTERN = AmountUnits.get_pattern()

# 定义基本金额单元模式
AMOUNT_UNIT_EXPR = fr'''
(?:
  (?:{COMPLEX_NUMBER_PATTERN}\s*{AMOUNT_UNIT_PATTERN})
)
'''

# 金额范围连接词，按符号类型分组
RANGE_TEXT_CONNECTORS = r'到|至|和|与|或|及'
RANGE_DASH_CONNECTORS = r'-|—|－|–|‐|‑|‒|⸺|⸻|﹘|﹣|⹀'
RANGE_TILDE_CONNECTORS = r'~|⁓|∼|～|∿|〜|〰'
RANGE_ARROW_CONNECTORS = r'→|➝|➞|⟶|⇒|⟹|⇾|⟾|⟿'
RANGE_OTHER_SYMBOLS = r'、|_|⁃|…|=|==|:|\\+|±'  # 转义加号
RANGE_COMPOUND_SYMBOLS = r'->|-->|=>'

# 金额范围连接词
AMOUNT_RANGE_CONNECTOR = r'(' + '|'.join([
    RANGE_TEXT_CONNECTORS,
    RANGE_DASH_CONNECTORS,
    RANGE_TILDE_CONNECTORS,
    RANGE_ARROW_CONNECTORS,
    RANGE_OTHER_SYMBOLS,
    RANGE_COMPOUND_SYMBOLS
]) + r')'

# 近似修饰前缀：出现在金额单位前的近似修饰词
PREFIX_APPROXIMATION = r'大约|大概|约|约为|粗略|大致|大致上|大体|概|粗略估计|姑且|权且|暂且'
PREFIX_PROXIMITY = r'接近|差不多|近|将近|几乎|接近于|濒临|靠近|邻近|左近|附近'
PREFIX_ESTIMATION = r'估计|粗算|算下来|看起来|算起来|目测|估摸|估量|佔计|目估|初步估计'
PREFIX_PREDICTION = r'预计|预估|预期|预测|可能|或许|也许|兴许|恐怕|大抵|想来|想必|料想|多半'
PREFIX_EQUIVALENCE = r'相当于|等同于|等于|等价于|好比|好像|仿佛|宛如|比如|相当于是|大约等于'
PREFIX_UNCERTAINTY = r'也就|大体上|基本|基本上|大体来说|总体来讲|笼统地说|大略|凡是|大体而言'

AMOUNT_APPROXIMATE_PREFIX = r'(' + '|'.join([
    PREFIX_APPROXIMATION,
    PREFIX_PROXIMITY,
    PREFIX_ESTIMATION,
    PREFIX_PREDICTION,
    PREFIX_EQUIVALENCE,
    PREFIX_UNCERTAINTY
]) + r')'

# 近似修饰后缀：出现在金额单位后的近似修饰词
SUFFIX_RANGE = r'左右|上下|内外|之间|前后|上下左右|区间|范围|附近|周边|界限'
SUFFIX_FLUCTUATION = r'上下浮动|摆布|上下波动|起伏|波动|飘忽|动荡|变动|起落'
SUFFIX_UNCERTAINTY = r'不等|不定|来回|变化|不一|不确定|待定|未定|不稳定|浮动|漂移'
SUFFIX_EXCESS = r'出头|出头儿|开外|多一点|有余|略多|稍多|略微超过|略超|略高|偏多'
SUFFIX_APPROXIMATION = r'几|许|多|大概|大约|估计|约摸|或者说|可以说|要么|要不|姑且算是'
SUFFIX_TIME = r'之久|时间|光景|时光|岁月|年头|时分|期间|一段时间'

AMOUNT_APPROXIMATE_SUFFIX = r'(' + '|'.join([
    SUFFIX_RANGE,
    SUFFIX_FLUCTUATION,
    SUFFIX_UNCERTAINTY,
    SUFFIX_EXCESS,
    SUFFIX_APPROXIMATION,
    SUFFIX_TIME 
]) + r')'

# 上限前缀修饰词：放在金额单位前，表示金额的最大限制
UPPER_LIMIT_NEGATION = r'不超过|不超過|不到|不足|不会超过|不会超過|不高于|不高於|不会多于|不会多於|不能超过|不能超過|不得超过|不应超过|不可超过|不能高于|不应高于'
UPPER_LIMIT_COMPARISON = r'少于|少於|低于|低於|小于|小於'
UPPER_LIMIT_MAX = r'最多|顶多|頂多|至多|最高|最大|最高不超过|最大不超|最多不超过|最高不超|最大不超过'
UPPER_LIMIT_BOUNDARY = r'上限是|上限为|封顶|限额|最高限额|最大值为'
UPPER_LIMIT_FINANCE = r'额度限制|最高可投|最高限额|投资上限|认购上限|申购限额'
UPPER_LIMIT_SYMBOLS = r'≤|<=|≦|<|＜'

AMOUNT_UPPER_LIMIT_PREFIX = r'(' + '|'.join([
    UPPER_LIMIT_NEGATION,
    UPPER_LIMIT_COMPARISON,
    UPPER_LIMIT_MAX,
    UPPER_LIMIT_BOUNDARY,
    UPPER_LIMIT_FINANCE,
    UPPER_LIMIT_SYMBOLS
]) + r')'

# 上限后缀修饰词：放在金额单位后，表示金额的最大限制
SUFFIX_BOUNDARY = r'以下|以内|之内|及以下|及以内|及之内|未满|之前|以前|不到|顶|为止|为限|止|之下|范围内|上限'
SUFFIX_CAP = r'封顶|封顶线|封顶值|封顶金额|最高限额'
SUFFIX_COMPARISON = r'或更少|及更少|或以下|及以下|或更低|及更低|或少于此数|以下|更低|更少|不超|最多|最高|最大'
SUFFIX_RESTRICTION = r'限制|不能再多|不再增加|档次以下|以下水平'
SUFFIX_SYMBOLS = r'↓'

AMOUNT_UPPER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_BOUNDARY,
    SUFFIX_CAP,
    SUFFIX_COMPARISON,
    SUFFIX_RESTRICTION,
    SUFFIX_SYMBOLS
]) + r')'

# 下限前缀修饰词：放在金额单位前，表示金额的最小限制
LOWER_LIMIT_MINIMUM = r'最少|至少|起码|最低|至少要|起步价|底价|底薪|最低限度|最少需要|至少需要'
LOWER_LIMIT_NEGATION = r'不少于|不少於|不低于|不低於|不会少于|不会少於|不能少于|不能少於|不小于|不小於|不得少于|不应少於'
LOWER_LIMIT_COMPARISON = r'多于|多於|高于|高於|大于|大於|超|超过|超出|多过|远超|高出|大过|大于等于'
LOWER_LIMIT_BOUNDARY = r'下限是|底线是|起始于|开始于|基准是|基线是|始于|入场券|门槛是|门槛为'
LOWER_LIMIT_SYMBOLS = r'>|≥|>=|＞|≧|⩾'

AMOUNT_LOWER_LIMIT_PREFIX = r'(' + '|'.join([
    LOWER_LIMIT_MINIMUM,
    LOWER_LIMIT_NEGATION,
    LOWER_LIMIT_COMPARISON,
    LOWER_LIMIT_BOUNDARY,
    LOWER_LIMIT_SYMBOLS
]) + r')'

# 下限后缀修饰词：放在金额单位后，表示金额的最小限制
SUFFIX_BOUNDARY = r'以上|之上|及以上|及之上|开外|起|起步|打底|为起点|起跳|开始|为基础|为底|为下限'
SUFFIX_FLOOR = r'最少|最低|底线|至少|底价|保底|起码|门槛|门坎|最低标准'
SUFFIX_DIRECTION = r'往上|向上|以上区间|以上水平|上浮'
SUFFIX_COMPARISON = r'或更多|及更多|或以上|及更高|甚至更高|乃至更多|不止|多于此数'
SUFFIX_FINANCE_THRESHOLD = r'起投|起购|起点金额|最低认购|最低申购|保本金额|保底收益|起存'
SUFFIX_SYMBOLS = r'↑|⬆|➚|↗|⥣|⟰|⇧|⇑'

AMOUNT_LOWER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_BOUNDARY,
    SUFFIX_FLOOR,
    SUFFIX_DIRECTION,
    SUFFIX_COMPARISON,
    SUFFIX_SYMBOLS,
    SUFFIX_FINANCE_THRESHOLD
]) + r')'

# 否定前缀：表示对后续金额表达的否定
AMOUNT_NEGATION_PREFIX = r'(不能|不会|不应该|不应|不可以|不可|不能够|不允许|不准|不得|禁止|严禁|不宜|不要|切忌|切莫|切勿|别|莫|勿|毋)'

# 金额超级模式：统一匹配各类金额表达式
AMOUNT_SUPER_PATTERN_STR = fr'''
(?: 
  # 否定前缀（可选）
  (?P<negation_prefix>{AMOUNT_NEGATION_PREFIX})?
  
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{AMOUNT_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{AMOUNT_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{AMOUNT_LOWER_LIMIT_PREFIX})?
  
  (?:
    # 格式3：单个金额单元（提高优先级，放在最前面）
    (?P<amount_unit>{AMOUNT_UNIT_EXPR})
    |
    # 格式1：数字+单位+连接词+数字+单位
    (?P<amount_unit1>{AMOUNT_UNIT_EXPR})
    (?P<connector>{AMOUNT_RANGE_CONNECTOR})
    (?P<amount_unit2>{AMOUNT_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+单位
    (?P<number1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{AMOUNT_RANGE_CONNECTOR})
    (?P<number2>{COMPLEX_NUMBER_PATTERN})
    (?P<shared_unit>{AMOUNT_UNIT_PATTERN})?
    |
    # 格式2: 纯数字格式(无需单位)，确保前面没有字母
    (?P<number_only>{COMPLEX_NUMBER_PATTERN})(?!{AMOUNT_UNIT_PATTERN})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{AMOUNT_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{AMOUNT_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{AMOUNT_APPROXIMATE_SUFFIX})?
)
'''

AMOUNT_SUPER_PATTERN_COMPILED = re.compile(AMOUNT_SUPER_PATTERN_STR, re.VERBOSE)

# 更新预编译的正则表达式字典，包含所有金额相关模式
COMPILED_AMOUNT_PATTERNS = {
    f"{AMOUNT_PREFIX}-super_pattern": AMOUNT_SUPER_PATTERN_COMPILED,
    f"{AMOUNT_PREFIX}-unit_pattern": re.compile(AMOUNT_UNIT_PATTERN),
    f"{AMOUNT_PREFIX}-range_connector": re.compile(AMOUNT_RANGE_CONNECTOR),
    f"{AMOUNT_PREFIX}-approximate_prefix": re.compile(AMOUNT_APPROXIMATE_PREFIX),
    f"{AMOUNT_PREFIX}-approximate_suffix": re.compile(AMOUNT_APPROXIMATE_SUFFIX),
    f"{AMOUNT_PREFIX}-upper_limit_prefix": re.compile(AMOUNT_UPPER_LIMIT_PREFIX),
    f"{AMOUNT_PREFIX}-upper_limit_suffix": re.compile(AMOUNT_UPPER_LIMIT_SUFFIX),
    f"{AMOUNT_PREFIX}-lower_limit_prefix": re.compile(AMOUNT_LOWER_LIMIT_PREFIX),
    f"{AMOUNT_PREFIX}-lower_limit_suffix": re.compile(AMOUNT_LOWER_LIMIT_SUFFIX),
    f"{AMOUNT_PREFIX}-negation_prefix": re.compile(AMOUNT_NEGATION_PREFIX)
}

if __name__ == "__main__":
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_AMOUNT_PATTERNS, "tool_regexs")