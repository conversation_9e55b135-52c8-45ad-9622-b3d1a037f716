"""
理财咨询场景时间抽取器

适用于提取金融文本中的时间信息，抽取两类时间范围：

1. 相对时间范围：如"最近七天"、"过去三到六个月"、"今年以来"等
2. 持续时间范围：如"一年半"、"90天到180天"、"三年左右"等

特别说明：
    1. 一年按366天计算，一个月按30天计算
    2. 相对时间范围的默认锚点是当前时间，除非有明确的时间锚点，当前时间锚点的天数默认为0，
       过去时间锚点的天数为负数，未来时间锚点的天数为正数。
    3. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，
       如果只有单个时间跨度描述，则最小最大值为该描述转天数。

模式设计：
    - 相对时间修饰词：相对某个时间锚点的描述，默认相对当前时间
        - 相对过去：
            prefix: [过去|最近|前|近|以来|至今|截至|截止|这|本|今|当前|現在|目前|此]
            suffix: [前|以前|之前|为止|以来]
            infix: [到|至|~|-|、|和|与|或|及]
            compound: [prefix] + 时间单位 + [infix] + [suffix]，如"过去三天以前"、"今年以来"
        - 相对未来：
            prefix: [接下来|未来|将来|即将|今后|往后]
            suffix: [后|以后|之后]
            infix: [到|至|~|-|、|和|与|或|及]
            compound: [prefix] + 时间单位 + [infix] + [suffix]，如"接下来三天之后"

    - 时间范围修饰词：对时间跨度范围的限定修饰
        - 上限：[不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|以下|以内|之内|内|未满]
        - 下限：[以上|之上|开外|起|最少|至少|不少于|不少於|多于|多於|起码]
        - 近似：[约|大约|大概|差不多|将近|接近|几乎|幾乎|左右|上下|多|出头|来]
        - 复合：<上限><近似><下限>

    - 时间跨度描述词：描述时间跨度的单位或数值
        - 基本单位：[天|日|来天|多天|几天|月|个月|個月|几月|来月|多个月|几个月|年|周年|来年|多年|几年|周|星期|礼拜|週|禮拜|来周|多周|几周|季度|季|个季度|個季度]
        - 数量描述：[一二三四五六七八九十百千万萬亿億兩壹贰叁肆伍陆柒捌玖拾佰仟]|[0-9]+]
        - 复合时间表达：如："三年零6个月"、"一千二百五六天"等

    - 时间锚点描述词：描述具体或模糊的时间锚点
        - 年份标记：[今年|去年|前年|明年|后年|[0-9]{4}年|本年|年初|年中|年末|年底|上半年|下半年]
        - 月份标记：[[一二三四五六七八九十]{1,2}月份|[1-9]月|1[0-2]月|本月|上个月|上個月|上月|上一个月|这个月|这一个月|月初|月中|月底|月末]
        - 季度标记：[第[一二三四]季度|[一二三四]季度|本季度|上个季度|上個季度|本财年|本財年|今年财年|今年財年|上季度|这个季度|这一季度|季初|季中|季末|季度末]
        - 周标记：[本周|上周|上週|上个星期|上個星期|星期[一二三四五六日天]|周[一二三四五六日天]|禮拜[一二三四五六日天]]
        - 日标记：[今天|昨天|前天|明天|后天|大前天|大后天|[1-9]号|[1-2][0-9]号|3[0-1]号|[1-9]日|[1-2][0-9]日|3[0-1]日]
        - 财年表达: [本财年|本財年|今年财年|今年財年]
        - 特殊时间点: [迄今为止|截止目前|至今|前几天|前幾天]

处理原则：
    1. 相对时间（relative_time_range） > 时间跨度（duration_time_range）
    2. 组合时间表达式 > 单一时间表达式
    3. 带修饰限定的 > 不带修饰限定的
    4. 长文本 > 短文本
    5. 原则1 > 原则2 > 原则3 > 原则4

处理流程：
    1. relative_time_range_extractor与duration_time_range_extractor分别提取相对时间与持续时间
    2. 在提取器内部不进行重叠片段处理，使用time_utils的create_time_result规范提取结果
    3. 
    
输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'relative_time_range' 或 'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""

from typing import Dict, List, Any
from app.utils.extractor.time.relative_time_extractor import RelativeTimeExtractor
from app.utils.extractor.time.duration_time_extractor import DurationTimeExtractor

class TimeExtractor:
    """
    时间抽取器，整合相对时间抽取器和持续时间抽取器
    提供完整的时间表达式提取功能
    """
    
    def __init__(self):
        """初始化时间抽取器"""
        self.relative_extractor = RelativeTimeExtractor()
        self.duration_extractor = DurationTimeExtractor()
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取所有时间表达式"""
        # 提取相对时间表达式
        relative_results = self.relative_extractor.extract_time(text)
        # 提取持续时间表达式
        duration_results = self.duration_extractor.extract_time(text)
        # 合并结果
        return relative_results + duration_results
    
    def extract_relative_time(self, text: str) -> List[Dict[str, Any]]:
        """仅从文本中提取相对时间表达式"""
        return self.relative_extractor.extract_time(text)
    
    def extract_duration_time(self, text: str) -> List[Dict[str, Any]]:
        """仅从文本中提取持续时间表达式"""
        return self.duration_extractor.extract_time(text)