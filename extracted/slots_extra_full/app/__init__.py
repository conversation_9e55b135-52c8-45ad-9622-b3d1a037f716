"""
应用包初始化文件
"""

import logging
from fastapi import FastAPI
from app.routes.slot_extractor_router import router as slot_extractor_router

from app.services.es_service import es_service

def create_app():
    # 初始化ES数据  
    if not es_service.init_es_data(force_rebuild_index=False):
        logging.error("初始化ES数据失败，服务可能无法正常工作")
        
    from app.services.cache_service import cache_service
    
    # 启动缓存服务
    cache_service.start()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="金融槽位提取服务",
        description="从用户输入中提取槽位信息",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # 注册路由
    app.include_router(slot_extractor_router, prefix="/api")
    
    return app
